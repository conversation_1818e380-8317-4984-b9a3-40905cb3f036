# AI Search Makefile

.PHONY: help build run test clean fmt vet deps

# 默认目标
help:
	@echo "AI Search - 火山AI搜索服务"
	@echo ""
	@echo "可用命令:"
	@echo "  build    - 编译项目"
	@echo "  run      - 运行服务"
	@echo "  test     - 运行测试"
	@echo "  clean    - 清理编译文件"
	@echo "  fmt      - 格式化代码"
	@echo "  vet      - 代码检查"
	@echo "  deps     - 安装依赖"
	@echo "  dev      - 开发模式运行"

# 编译项目
build:
	@echo "🔨 编译项目..."
	go build -o ai-search .

# 运行服务
run: build
	@echo "🚀 启动服务..."
	./ai-search

# 开发模式运行
dev:
	@echo "🔧 开发模式启动..."
	go run main.go

# 运行测试
test:
	@echo "🧪 运行测试..."
	go test -v ./...

# 清理编译文件
clean:
	@echo "🧹 清理编译文件..."
	rm -f ai-search
	go clean

# 格式化代码
fmt:
	@echo "📝 格式化代码..."
	go fmt ./...

# 代码检查
vet:
	@echo "🔍 代码检查..."
	go vet ./...

# 安装依赖
deps:
	@echo "📦 安装依赖..."
	go mod tidy
	go mod download

# 完整检查
check: fmt vet test
	@echo "✅ 代码检查完成"

# 构建和测试
all: deps check build
	@echo "🎉 构建完成"
