<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能搜索 - 火山AI搜索</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .nav {
            background: #f8f9fa;
            padding: 15px 30px;
            border-bottom: 1px solid #e0e0e0;
        }

        .nav a {
            text-decoration: none;
            color: #667eea;
            margin-right: 20px;
            padding: 8px 16px;
            border-radius: 5px;
            transition: background-color 0.3s;
        }

        .nav a:hover, .nav a.active {
            background-color: #667eea;
            color: white;
        }

        .content {
            padding: 30px;
        }

        .search-section {
            margin-bottom: 30px;
            padding: 25px;
            border: 1px solid #e0e0e0;
            border-radius: 10px;
            background: #fafafa;
        }

        .search-section h2 {
            color: #333;
            margin-bottom: 20px;
            font-size: 1.5em;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #555;
        }

        input, button {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s;
        }

        input:focus {
            outline: none;
            border-color: #667eea;
        }

        button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            cursor: pointer;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
            transition: transform 0.2s, box-shadow 0.2s;
        }

        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .loading {
            display: none;
            text-align: center;
            color: #667eea;
            font-weight: 600;
            margin: 20px 0;
        }

        .results {
            margin-top: 30px;
        }

        .result-item {
            background: white;
            border: 1px solid #e0e0e0;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: transform 0.2s;
        }

        .result-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 20px rgba(0,0,0,0.15);
        }

        .result-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 15px;
        }

        .result-title {
            font-size: 1.2em;
            font-weight: 600;
            color: #333;
            margin-bottom: 5px;
        }

        .result-category {
            background: #667eea;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8em;
        }

        .result-content {
            color: #666;
            line-height: 1.6;
            margin-bottom: 15px;
        }

        .result-images {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
            gap: 15px;
            margin-top: 15px;
            padding: 10px;
            background: #f9f9f9;
            border-radius: 8px;
        }

        .result-image {
            width: 100%;
            height: 120px;
            object-fit: cover;
            border-radius: 8px;
            border: 2px solid #e0e0e0;
            cursor: pointer;
            transition: transform 0.2s, box-shadow 0.2s;
        }

        .result-image:hover {
            transform: scale(1.05);
            border-color: #667eea;
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }

        .result-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 0.9em;
            color: #888;
            border-top: 1px solid #f0f0f0;
            padding-top: 10px;
        }

        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }

        .no-results {
            text-align: center;
            color: #666;
            font-style: italic;
            padding: 40px;
        }

        .image-modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.8);
        }

        .modal-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            max-width: 90%;
            max-height: 90%;
        }

        .modal-image {
            width: 100%;
            height: auto;
            border-radius: 10px;
        }

        .close {
            position: absolute;
            top: 15px;
            right: 35px;
            color: #f1f1f1;
            font-size: 40px;
            font-weight: bold;
            cursor: pointer;
        }

        .close:hover {
            color: #667eea;
        }

        @media (max-width: 768px) {
            .container {
                margin: 10px;
            }

            .content {
                padding: 20px;
            }

            .result-header {
                flex-direction: column;
                align-items: flex-start;
            }

            .result-category {
                margin-top: 10px;
            }

            .result-images {
                grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
                gap: 10px;
                padding: 8px;
            }

            .result-image {
                height: 100px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 智能搜索</h1>
            <p>基于火山AI的智能搜索服务</p>
        </div>

        <div class="nav">
            <a href="/">数据管理</a>
            <a href="/search" class="active">智能搜索</a>
            <a href="/chat">对话式搜索</a>
        </div>

        <div class="content">
            <div class="search-section">
                <h2>🔍 智能搜索</h2>
                <div class="form-group">
                    <label for="searchQuery">搜索查询:</label>
                    <input type="text" id="searchQuery" placeholder="输入搜索关键词" onkeypress="handleEnterKey(event)">
                </div>
                <div class="form-group">
                    <label for="searchLimit">返回结果数量:</label>
                    <input type="number" id="searchLimit" value="10" min="1" max="100">
                </div>
                <button onclick="searchDataset()">开始搜索</button>
                <div class="loading" id="searchLoading">🔍 正在搜索...</div>
            </div>

            <div id="searchResults" class="results"></div>
        </div>
    </div>

    <!-- 图片模态框 -->
    <div id="imageModal" class="image-modal">
        <span class="close" onclick="closeModal()">&times;</span>
        <div class="modal-content">
            <img id="modalImage" class="modal-image" src="" alt="">
        </div>
    </div>

    <script>
        function handleEnterKey(event) {
            if (event.key === 'Enter') {
                searchDataset();
            }
        }

        async function searchDataset() {
            const query = document.getElementById('searchQuery').value;
            const limit = parseInt(document.getElementById('searchLimit').value);
            const loading = document.getElementById('searchLoading');
            const resultsContainer = document.getElementById('searchResults');

            if (!query.trim()) {
                showError('请输入搜索查询');
                return;
            }

            loading.style.display = 'block';
            resultsContainer.innerHTML = '';

            try {
                const response = await fetch('/api/search', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ query, limit })
                });

                const result = await response.json();

                loading.style.display = 'none';

                if (response.ok) {
                    displaySearchResults(result);
                } else {
                    showError(`搜索失败: ${result.error || '未知错误'}`);
                }

            } catch (error) {
                loading.style.display = 'none';
                showError(`请求失败: ${error.message}`);
            }
        }

        function displaySearchResults(data) {
            const resultsContainer = document.getElementById('searchResults');

            if (!data.result || !data.result.search_results || data.result.search_results.length === 0) {
                resultsContainer.innerHTML = '<div class="no-results">未找到相关结果</div>';
                return;
            }

            const results = data.result.search_results;
            let html = `<h3>搜索结果 (共 ${data.result.total_items} 条)</h3>`;

            results.forEach((item, index) => {
                const displayFields = item.display_fields || {};
                const title = displayFields.title || displayFields.item_id || `结果 ${index + 1}`;
                const category = displayFields.category || '未分类';
                const images = displayFields.images || [];

                html += `
                    <div class="result-item">
                        <div class="result-header">
                            <div>
                                <div class="result-title">${escapeHtml(title)}</div>
                                <div class="result-category">${escapeHtml(category)}</div>
                            </div>
                        </div>
                        <div class="result-content">
                            ID: ${escapeHtml(item._id || item.id)}
                        </div>

                        <div class="result-images">
                             <img src="${escapeHtml(displayFields.url)}"
                                        alt="搜索结果图片"
                                        class="result-image"
                                        onclick="openModal('${escapeHtml(displayFields.url)}')"
                                        onerror="this.style.display='none'">
                        </div>

                        <div class="result-meta">
                            <span>状态: ${displayFields.status === 1 ? '正常' : '未知'}</span>
                            <span>ID: ${escapeHtml(displayFields.item_id || item._id)}</span>
                        </div>
                    </div>
                `;
            });

            resultsContainer.innerHTML = html;
        }

        function showError(message) {
            const resultsContainer = document.getElementById('searchResults');
            resultsContainer.innerHTML = `<div class="error">${escapeHtml(message)}</div>`;
        }

        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        function openModal(imageUrl) {
            const modal = document.getElementById('imageModal');
            const modalImage = document.getElementById('modalImage');
            modalImage.src = imageUrl;
            modal.style.display = 'block';
        }

        function closeModal() {
            const modal = document.getElementById('imageModal');
            modal.style.display = 'none';
        }

        // 点击模态框外部关闭
        window.onclick = function(event) {
            const modal = document.getElementById('imageModal');
            if (event.target === modal) {
                closeModal();
            }
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('智能搜索页面已加载');
        });
    </script>
</body>
</html>
