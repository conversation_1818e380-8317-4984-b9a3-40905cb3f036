# API 使用示例

本文档提供了火山AI搜索服务各个API接口的详细使用示例。

## 🔧 基础配置

确保您的 `Config/config.json` 文件已正确配置：

```json
{
  "access_key": "your_access_key",
  "secret_key": "your_secret_key",
  "dataset_id": "your_dataset_id",
  "application_id": "your_application_id",
  "base_url": "https://aisearch.cn-beijing.volces.com/api/v1"
}
```

## 📝 数据写入示例

### 写入单个文档
```bash
curl -X POST http://localhost:8080/api/write \
  -H "Content-Type: application/json" \
  -d '{
    "documents": [
      {
        "id": "doc_001",
        "content": {
          "title": "人工智能基础",
          "text": "人工智能是计算机科学的一个分支，致力于创建能够执行通常需要人类智能的任务的系统。",
          "category": "技术",
          "tags": ["AI", "机器学习", "深度学习"]
        }
      }
    ]
  }'
```

### 批量写入文档
```bash
curl -X POST http://localhost:8080/api/write \
  -H "Content-Type: application/json" \
  -d '{
    "documents": [
      {
        "id": "doc_002",
        "content": {
          "title": "机器学习算法",
          "text": "机器学习是人工智能的一个子集，通过算法让计算机从数据中学习模式。",
          "category": "技术"
        }
      },
      {
        "id": "doc_003",
        "content": {
          "title": "深度学习网络",
          "text": "深度学习使用多层神经网络来模拟人脑的学习过程。",
          "category": "技术"
        }
      }
    ]
  }'
```

## 🗑️ 数据删除示例

### 删除指定文档
```bash
curl -X POST http://localhost:8080/api/delete \
  -H "Content-Type: application/json" \
  -d '{
    "document_ids": ["doc_001", "doc_002"]
  }'
```

## 🔍 智能搜索示例

### 基础搜索
```bash
curl -X POST http://localhost:8080/api/search \
  -H "Content-Type: application/json" \
  -d '{
    "query": "人工智能",
    "limit": 10
  }'
```

### 响应示例
```json
{
  "request_id": "req_12345",
  "result": {
    "search_results": [
      {
        "_id": "doc_001",
        "display_fields": {
          "item_id": "doc_001",
          "title": "人工智能基础",
          "category": "技术",
          "status": 1
        }
      }
    ],
    "total_items": 1
  }
}
```

## 💬 对话式搜索示例

### 基础对话搜索
```bash
curl -X POST http://localhost:8080/api/chat_search \
  -H "Content-Type: application/json" \
  -d '{
    "query": "什么是人工智能？",
    "limit": 5,
    "temperature": 0.7
  }'
```

### 带会话上下文的对话搜索
```bash
curl -X POST http://localhost:8080/api/chat_search \
  -H "Content-Type: application/json" \
  -d '{
    "query": "它有哪些应用领域？",
    "limit": 5,
    "session_id": "session_12345",
    "context": {
      "previous_topic": "人工智能",
      "user_level": "初学者"
    },
    "temperature": 0.8
  }'
```

### 对话搜索响应示例
```json
{
  "request_id": "req_67890",
  "result": {
    "answer": "人工智能是计算机科学的一个分支，致力于创建能够执行通常需要人类智能的任务的系统。它包括机器学习、深度学习、自然语言处理等多个子领域。",
    "session_id": "session_12345",
    "search_results": [
      {
        "_id": "doc_001",
        "display_fields": {
          "item_id": "doc_001",
          "title": "人工智能基础",
          "category": "技术",
          "status": 1
        },
        "score": 0.95
      }
    ],
    "total_items": 1
  }
}
```

## ✨ 搜索词补全示例

### 获取搜索建议
```bash
curl -X POST http://localhost:8080/api/query_completion \
  -H "Content-Type: application/json" \
  -d '{
    "query": "人工智",
    "limit": 5
  }'
```

### 补全响应示例
```json
{
  "request_id": "req_completion_123",
  "result": {
    "completions": [
      {
        "text": "人工智能",
        "score": 0.95
      },
      {
        "text": "人工智能算法",
        "score": 0.88
      },
      {
        "text": "人工智能应用",
        "score": 0.82
      },
      {
        "text": "人工智能发展",
        "score": 0.75
      },
      {
        "text": "人工智能技术",
        "score": 0.70
      }
    ]
  }
}
```

## 🏥 健康检查示例

```bash
curl -X GET http://localhost:8080/health
```

### 响应示例
```json
{
  "status": "ok",
  "message": "AI Search Service is running"
}
```

## 🔧 JavaScript 前端示例

### 对话式搜索
```javascript
async function chatSearch(query, sessionId = null) {
  const requestData = {
    query: query,
    limit: 10,
    temperature: 0.7
  };
  
  if (sessionId) {
    requestData.session_id = sessionId;
  }
  
  try {
    const response = await fetch('/api/chat_search', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestData)
    });
    
    const result = await response.json();
    console.log('对话搜索结果:', result);
    return result;
  } catch (error) {
    console.error('对话搜索失败:', error);
  }
}
```

### 搜索词补全
```javascript
async function getQueryCompletion(partialQuery) {
  try {
    const response = await fetch('/api/query_completion', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        query: partialQuery,
        limit: 5
      })
    });
    
    const result = await response.json();
    console.log('补全建议:', result);
    return result;
  } catch (error) {
    console.error('获取补全失败:', error);
  }
}
```

## 📊 参数说明

### 对话式搜索参数
- `query`: 搜索查询文本（必需）
- `limit`: 返回结果数量（可选，默认10）
- `session_id`: 会话ID，用于保持对话上下文（可选）
- `context`: 额外的上下文信息（可选）
- `temperature`: 生成答案的随机性，0.1-1.0（可选，默认0.7）

### 搜索词补全参数
- `query`: 部分查询文本（必需）
- `limit`: 返回补全建议数量（可选，默认5）

## ⚠️ 注意事项

1. 所有API都需要正确的认证配置
2. 对话式搜索的会话ID可以用于保持多轮对话的上下文
3. 温度参数影响生成答案的创造性，值越高越有创造性
4. 搜索词补全适用于实时输入建议功能
5. 建议在生产环境中添加适当的错误处理和重试机制
