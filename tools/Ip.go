package tools

import (
	"errors"
	"net"
	"net/http"
	"strings"
)

// GetLocalIP 返回本机的局域网 IPv4 地址
func GetLocalIP() (string, error) {
	addrs, err := net.InterfaceAddrs()
	if err != nil {
		return "", err
	}

	for _, addr := range addrs {
		if ipNet, ok := addr.(*net.IPNet); ok && !ipNet.IP.IsLoopback() {
			if ip4 := ipNet.IP.To4(); ip4 != nil {
				return ip4.String(), nil
			}
		}
	}
	return "", errors.New("无法找到本机 IP")
}

// GetClientIP 从 http.Request 中提取客户端真实 IP 地址
func GetClientIP(r *http.Request) string {
	// 常见的代理头
	ip := r.Header.Get("X-Forwarded-For")
	if ip != "" {
		// 多个 IP 取第一个
		ips := strings.Split(ip, ",")
		return strings.TrimSpace(ips[0])
	}

	ip = r.Header.Get("X-Real-IP")
	if ip != "" {
		return ip
	}

	// Fallback 到 RemoteAddr
	host, _, err := net.SplitHostPort(r.RemoteAddr)
	if err != nil {
		return r.RemoteAddr // 返回原始 RemoteAddr
	}
	return host
}
