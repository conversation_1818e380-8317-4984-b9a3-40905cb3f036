package clients

import (
	"ai-search/config"
	"testing"
)

func TestSearchRequest(t *testing.T) {
	cfg, err := config.LoadConfig("../config/config.json")
	if err != nil {
		t.Fatalf("Failed to load config: %v", err)
	}
	cfg.Debug = true
	client := &DefaultVolcesClient{
		Config: cfg,
	}
	t.Log(client.SearchDataset("test query", 10))
}

func TestQueryCompletion(t *testing.T) {
	cfg, err := config.LoadConfig("../config/config.json")
	if err != nil {
		t.Fatalf("Failed to load config: %v", err)
	}
	cfg.Debug = true
	client := &DefaultVolcesClient{
		Config: cfg,
	}
	t.Log(client.QueryCompletion("test query", 10))
}
