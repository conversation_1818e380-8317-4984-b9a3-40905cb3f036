package clients

import (
	"ai-search/tools"
	"bytes"
	"encoding/json"
	"fmt"
	"github.com/google/uuid"
	"net/http"
	"net/url"
	"strings"

	"ai-search/app"
	"ai-search/config"

	"github.com/guonaihong/gout"
	"github.com/volcengine/volc-sdk-golang/base"
)

// DefaultVolcesClient 默认的火山AI客户端实现
type DefaultVolcesClient struct {
	Config *config.Config
}

// NewDefaultVolcesClient 创建新的默认客户端
func NewDefaultVolcesClient(cfg *config.Config) *DefaultVolcesClient {
	return &DefaultVolcesClient{
		Config: cfg,
	}
}

// WriteDataset 实现写入数据集
func (c *DefaultVolcesClient) WriteDataset(documents []app.Document) (*app.DocumentResponse, error) {
	body, err := json.Marshal(map[string]interface{}{"documents": documents})
	if err != nil {
		return nil, fmt.Errorf("marshal documents error: %w", err)
	}
	endpoint := fmt.Sprintf("/dataset/%s/write", c.Config.DatasetID)
	res := &app.DocumentResponse{}
	return res, c.makeRequest(endpoint, body, res)
}

// DeleteDataset 实现删除数据集
func (c *DefaultVolcesClient) DeleteDataset(documentIDs []string) (*app.DocumentResponse, error) {
	body, err := json.Marshal(documentIDs)
	if err != nil {
		return nil, fmt.Errorf("marshal document IDs error: %w", err)
	}
	endpoint := fmt.Sprintf("/dataset/%s/delete", c.Config.DatasetID)
	res := &app.DocumentResponse{}
	return res, c.makeRequest(endpoint, body, res)
}

type SearchRequest struct {
	Query      *SearchQuery  `json:"query"`
	PageNumber int           `json:"page_number"`
	PageSize   int           `json:"page_size"`
	User       *SearchUser   `json:"user"`
	Filter     *SearchFilter `json:"filter,optional,omitempty"`
	DatasetId  string        `json:"dataset_id"`
}

type SearchQuery struct {
	Text string `json:"text,omitempty"`
}

type SearchUser struct {
	UserId string `json:"user_id"`
}

type SearchFilter struct {
	Op    string `json:"op"`
	Field string `json:"field"`
	Conds []int  `json:"conds"`
}

// SearchDataset 实现搜索数据集
func (c *DefaultVolcesClient) SearchDataset(query string, limit int) (*app.SearchResponse, error) {

	// 获取ip
	ip, _ := tools.GetLocalIP()
	body := SearchRequest{
		Query: &SearchQuery{
			Text: query,
		},
		PageNumber: 1,
		PageSize:   limit,
		User: &SearchUser{
			UserId: ip,
		},
		DatasetId: c.Config.DatasetID,
	}
	endpoint := fmt.Sprintf("/application/%s/search", c.Config.ApplicationID)
	res := &app.SearchResponse{}
	return res, c.makeRequest(endpoint, body, res)
}

// ChatSearchDataset 实现对话式搜索数据集
func (c *DefaultVolcesClient) ChatSearchDataset(query string, limit int, sessionID string, context map[string]string, temperature float64) (*app.ChatSearchResponse, error) {
	// 获取ip
	ip, _ := tools.GetLocalIP()

	body := map[string]interface{}{
		"query": map[string]interface{}{
			"text": query,
		},
		"page_number": 1,
		"page_size":   limit,
		"user": map[string]interface{}{
			"user_id": ip,
		},
		"dataset_id": c.Config.DatasetID,
	}

	// 添加会话ID
	if sessionID != "" {
		body["session_id"] = sessionID
	} else {
		body["session_id"] = uuid.ClockSequence()
	}

	// 添加上下文
	if len(context) > 0 {
		body["context"] = context
	}

	// 添加温度参数
	if temperature > 0 {
		body["temperature"] = temperature
	}

	endpoint := fmt.Sprintf("/application/%s/chat_search", c.Config.ApplicationID)
	res := &app.ChatSearchResponse{}
	return res, c.makeRequest(endpoint, body, res)
}

// QueryCompletion 实现搜索词补全
func (c *DefaultVolcesClient) QueryCompletion(query string, limit int) (*app.QueryCompletionResponse, error) {
	body := map[string]interface{}{
		"query": query,
		"limit": limit,
	}

	endpoint := fmt.Sprintf("/application/%s/query_completion", c.Config.ApplicationID)
	res := &app.QueryCompletionResponse{}
	return res, c.makeRequest(endpoint, body, res)
}

// makeRequest 发起HTTP请求
func (c *DefaultVolcesClient) makeRequest(endpoint string, body any, Response any) error {
	req := gout.
		POST(fmt.Sprintf("%s%s", c.Config.BaseURL, endpoint)).
		SetHeader(gout.H{
			"Accept":       "application/json",
			"Content-Type": "application/json",
		}).
		SetJSON(body).
		RequestUse(
			&VolcesRequestMiddler{c.Config},
		)
	if c.Config.Debug {
		req = req.Debug(true)
	}
	return req.BindJSON(&Response).Do()
}

// VolcesRequestMiddler 火山引擎请求中间件
type VolcesRequestMiddler struct {
	Config *config.Config
}

// ModifyRequest 修改请求，添加签名
func (d *VolcesRequestMiddler) ModifyRequest(req *http.Request) error {
	credential := base.Credentials{
		AccessKeyID:     d.Config.AccessKey,
		SecretAccessKey: d.Config.SecretKey,
		Service:         "aisearch",
		Region:          "cn-north-1",
	}
	req = credential.Sign(req)
	return nil
}

// PrepareRequest 准备请求（备用方法）
func (c *DefaultVolcesClient) PrepareRequest(method string, endpoint string, query url.Values, body []byte) *http.Request {
	u, err := url.Parse(endpoint)
	if err != nil {
		panic(err)
	}

	if query != nil {
		u.RawQuery = query.Encode()
	}

	req, _ := http.NewRequest(strings.ToUpper(method), u.String(), bytes.NewReader(body))
	req.Header.Add("Accept", "application/json")
	req.Header.Add("Content-Type", "application/json")
	req.Header.Add("Host", u.Host)

	credential := base.Credentials{
		AccessKeyID:     c.Config.AccessKey,
		SecretAccessKey: c.Config.SecretKey,
		Service:         "aisearch",
		Region:          "cn-north-1",
	}
	req = credential.Sign(req)
	return req
}
