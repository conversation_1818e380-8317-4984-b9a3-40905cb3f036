# AI Search - 火山AI搜索服务

一个基于火山引擎AI搜索的Web服务，提供数据集管理和智能搜索功能。

## 🏗️ 项目结构

```
ai-search/
├── main.go                 # 主入口文件
├── go.mod                  # Go模块依赖
├── go.sum                  # 依赖校验文件
├── index.html              # 前端演示页面
├── Config/                 # 配置模块
│   ├── config.go          # 配置结构和加载逻辑
│   └── config.json        # 配置文件
├── App/                    # 应用核心模块
│   ├── models.go          # 数据模型定义
│   ├── handlers.go        # HTTP处理器
│   └── router.go          # 路由设置
└── Clients/                # 客户端模块
    └── volces_client.go   # 火山引擎客户端实现
```

## 🚀 功能特性

- **数据写入**: 向数据集写入文档数据
- **数据删除**: 从数据集删除指定文档
- **智能搜索**: 基于查询文本进行智能搜索
- **对话式搜索**: 支持上下文的对话式智能搜索
- **搜索词补全**: 智能搜索词补全建议
- **Web界面**: 提供友好的Web演示界面
- **健康检查**: 服务状态监控接口

## 📋 API接口

### 1. 写入数据
```http
POST /api/write
Content-Type: application/json

{
  "documents": [
    {
      "id": "doc1",
      "content": {
        "title": "示例标题",
        "text": "这是示例内容"
      }
    }
  ]
}
```

### 2. 删除数据
```http
POST /api/delete
Content-Type: application/json

{
  "document_ids": ["doc1", "doc2"]
}
```

### 3. 搜索数据
```http
POST /api/search
Content-Type: application/json

{
  "query": "搜索关键词",
  "limit": 10
}
```

### 4. 对话式搜索
```http
POST /api/chat_search
Content-Type: application/json

{
  "query": "搜索关键词",
  "limit": 10,
  "session_id": "session_123",
  "context": {
    "user_preference": "技术文档"
  },
  "temperature": 0.7
}
```

### 5. 搜索词补全
```http
POST /api/query_completion
Content-Type: application/json

{
  "query": "搜索关键",
  "limit": 5
}
```

### 6. 健康检查
```http
GET /health
```

## ⚙️ 配置说明

配置文件位于 `Config/config.json`：

```json
{
  "access_key": "你的访问密钥",
  "secret_key": "你的秘密密钥",
  "dataset_id": "数据集ID",
  "application_id": "应用ID",
  "base_url": "https://aisearch.cn-beijing.volces.com/api/v1"
}
```

## 🛠️ 安装和运行

### 1. 安装依赖
```bash
go mod tidy
```

### 2. 配置服务
编辑 `Config/config.json` 文件，填入你的火山引擎配置信息。

### 3. 运行服务
```bash
go run main.go
```

服务将在 `http://localhost:8080` 启动。

### 4. 访问Web界面
打开浏览器访问 `http://localhost:8080` 查看演示界面。

## 🏛️ 架构设计

### 模块化设计
- **Config**: 配置管理，支持配置验证和加载
- **App**: 应用核心，包含数据模型、处理器和路由
- **Clients**: 客户端实现，封装火山引擎API调用

### 接口设计
- `VolcesClient`: 定义火山AI客户端接口，便于扩展和测试
- 支持自定义客户端实现，只需实现 `VolcesClient` 接口

### 错误处理
- 统一的错误响应格式
- 详细的错误信息和状态码
- 请求参数验证

## 🔧 自定义扩展

### 自定义客户端
如果需要自定义认证或请求逻辑，可以实现 `app.VolcesClient` 接口：

```go
type CustomVolcesClient struct {
    // 自定义字段
}

func (c *CustomVolcesClient) WriteDataset(documents []app.Document) (*http.Response, error) {
    // 自定义实现
}

func (c *CustomVolcesClient) DeleteDataset(documentIDs []string) (*http.Response, error) {
    // 自定义实现
}

func (c *CustomVolcesClient) SearchDataset(query string, limit int) (*http.Response, error) {
    // 自定义实现
}
```

然后在 `main.go` 中替换客户端：
```go
// volcesClient := clients.NewDefaultVolcesClient(cfg)
volcesClient := &CustomVolcesClient{}
```

## 📝 开发说明

### 代码规范
- 使用Go标准代码格式
- 遵循包命名规范
- 添加适当的注释和文档

### 测试
建议为各个模块编写单元测试，特别是：
- 配置加载和验证
- HTTP处理器逻辑
- 客户端接口实现

## 🔗 火山引擎API参考

### 鉴权机制
使用 AK/SK 鉴权机制

### 核心API端点
- **数据集写入**: `POST /dataset/{dataset_id}/write`
- **数据集删除**: `POST /dataset/{dataset_id}/delete`
- **数据集查询**: `POST /application/{application_id}/search`
- **对话式查询**: `POST /application/{application_id}/chat_search`
- **搜索词补全**: `POST /application/{application_id}/query_completion`