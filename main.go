package main

import (
	"fmt"
	"log"

	"ai-search/app"
	"ai-search/clients"
	"ai-search/config"
)

func main() {
	// 加载配置
	cfg, err := config.LoadConfig("")
	if err != nil {
		log.Fatal("Failed to load config:", err)
	}

	// 验证配置
	if err := cfg.Validate(); err != nil {
		log.Fatal("Invalid config:", err)
	}

	// 初始化火山AI客户端
	volcesClient := clients.NewDefaultVolcesClient(cfg)

	// 创建处理器
	handler := app.NewHandler(volcesClient)

	// 设置路由
	router := app.SetupRouter(handler)

	// 启动服务器
	fmt.Println("🚀 服务器启动在 http://localhost:8080")
	fmt.Println("📖 API文档:")
	fmt.Println("  POST /api/write            - 写入数据")
	fmt.Println("  POST /api/delete           - 删除数据")
	fmt.Println("  POST /api/search           - 搜索数据")
	fmt.Println("  POST /api/chat_search      - 对话式搜索")
	fmt.Println("  POST /api/query_completion - 搜索词补全")
	fmt.Println("  GET  /health               - 健康检查")

	if err := router.Run(":8080"); err != nil {
		log.Fatal("Failed to start server:", err)
	}
}
