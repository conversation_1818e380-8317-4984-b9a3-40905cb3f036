package app

import (
	"net/http"

	"github.com/gin-gonic/gin"
)

// Handler 处理器结构
type Handler struct {
	VolcesClient VolcesClient
}

// NewHandler 创建新的处理器
func NewHandler(client VolcesClient) *Handler {
	return &Handler{
		VolcesClient: client,
	}
}

// WriteDataset 处理数据写入请求
func (h *Handler) WriteDataset(c *gin.Context) {
	var req WriteRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid request format",
			"details": err.Error(),
		})
		return
	}

	// 验证请求数据
	if len(req.Documents) == 0 {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Documents cannot be empty",
		})
		return
	}

	resp, err := h.VolcesClient.WriteDataset(req.Documents)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to write dataset",
			"details": err.Error(),
		})
		return
	}
	c.<PERSON>(http.StatusOK, resp)
}

// DeleteDataset 处理数据删除请求
func (h *Handler) DeleteDataset(c *gin.Context) {
	var req DeleteRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid request format",
			"details": err.Error(),
		})
		return
	}

	// 验证请求数据
	if len(req.DocumentIDs) == 0 {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Document IDs cannot be empty",
		})
		return
	}

	resp, err := h.VolcesClient.DeleteDataset(req.DocumentIDs)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to delete dataset",
			"details": err.Error(),
		})
		return
	}
	c.JSON(http.StatusOK, resp)
}

// SearchDataset 处理数据搜索请求
func (h *Handler) SearchDataset(c *gin.Context) {
	var req SearchRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid request format",
			"details": err.Error(),
		})
		return
	}

	// 验证请求数据
	if req.Query == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Query cannot be empty",
		})
		return
	}

	// 设置默认限制
	if req.Limit <= 0 {
		req.Limit = 10
	}

	resp, err := h.VolcesClient.SearchDataset(req.Query, req.Limit)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to search dataset",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, resp)
}

// ChatSearchDataset 处理对话式搜索请求
func (h *Handler) ChatSearchDataset(c *gin.Context) {
	var req ChatSearchRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid request format",
			"details": err.Error(),
		})
		return
	}

	// 验证请求数据
	if req.Query == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Query cannot be empty",
		})
		return
	}

	// 设置默认限制
	if req.Limit <= 0 {
		req.Limit = 10
	}

	// 设置默认温度
	if req.Temperature <= 0 {
		req.Temperature = 0.7
	}

	resp, err := h.VolcesClient.ChatSearchDataset(req.Query, req.Limit, req.SessionID, req.Context, req.Temperature)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to chat search dataset",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, resp)
}

// QueryCompletion 处理搜索词补全请求
func (h *Handler) QueryCompletion(c *gin.Context) {
	var req QueryCompletionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid request format",
			"details": err.Error(),
		})
		return
	}

	// 验证请求数据
	if req.Query == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Query cannot be empty",
		})
		return
	}

	// 设置默认限制
	if req.Limit <= 0 {
		req.Limit = 5
	}

	resp, err := h.VolcesClient.QueryCompletion(req.Query, req.Limit)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to get query completion",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, resp)
}
